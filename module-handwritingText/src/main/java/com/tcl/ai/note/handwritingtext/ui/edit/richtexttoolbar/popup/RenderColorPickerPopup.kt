package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup

import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.material.AppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.ui.utils.rememberKeyboardState
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.ui.popup.AnimatedPopupWithShadow
import com.tcl.ai.note.handwritingtext.ui.popup.PureColorPalettePopup
import com.tcl.ai.note.handwritingtext.ui.richtext.component.BackgroundColorPicker
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextColorPicker
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx


/**
 * 渲染颜色选择器弹出框
 * 根据工具类型显示相应的颜色选择器弹窗
 *
 * @param item 颜色选择器项
 * @param state 工具栏状态
 * @param areaHeight 可用区域高度（像素）
 */
@Composable
internal fun RenderColorPickerPopup(
    item: RichTextToolBarItem.ColorPicker,
    state: RichTextToolBarState,
    areaHeight: Int,
    menuBarItem: MenuBarItem? = null
) {
    var isShowColorPicker  by remember { mutableStateOf(false) }
    when (item.toolType) {
        RichTextToolType.TEXT_COLOR -> {
            if (state.showTextColorPicker && !isShowColorPicker) {
                val density = LocalDensity.current
                val screenWidthDp = GlobalContext.screenWidth.px2dp
                val dimens = getGlobalDimens()

                val popupWidth = 326.dp // TextColorPicker 宽度
                val popupHeight = 129.dp // TextColorPicker 高度
                val space = 8.dp

                val keyboardState by rememberKeyboardState()
                val insets = AppBarDefaults.bottomAppBarWindowInsets
                val bottomInsetPx = insets.getBottom(LocalDensity.current)
                val bottomInsetDp = with(density) { bottomInsetPx.toDp() }
                val keyBoardHeight = keyboardState.isVisible.judge(
                    keyboardState.height - bottomInsetDp,
                    0.dp
                )

                val xOffset =  with(density) {
                    // 屏幕水平居中
                    val screenWidth = screenWidthDp.dp.toPx()
                    ((screenWidth - popupWidth.toPx()) / 2).toInt()
                }

                val yOffset = areaHeight - (popupHeight + space + keyBoardHeight).toPx

                AnimatedPopupWithShadow(
                    onDismissRequest = {
                        item.onToggleExpanded(item)
                    },
                    offset = IntOffset(xOffset, yOffset.toInt())
                ) {
                    Box(
                        modifier = Modifier
                    ) {
                        TextColorPicker(
                            colors = state.richTextRecentColors,
                            selectedColor = state.textColor,
                            onColorSelected = { color -> item.onColorChange(color, item) },
                            onOpenColorPicker = {
                                isShowColorPicker = true
                            },
                            onClose = {
                                item.onToggleExpanded(item)
                            }

                        )
                    }
                }
            }
        }
        RichTextToolType.TEXT_BG_COLOR -> {
            if (state.showTextBgColorPicker) {
                val density = LocalDensity.current
                val screenWidthDp = GlobalContext.screenWidth.px2dp
                val dimens = getGlobalDimens()

                val popupWidth = 257.dp // BackgroundColorPicker 宽度
                val popupHeight = 80.dp // BackgroundColorPicker 高度
                val space = 8.dp

                val keyboardState by rememberKeyboardState()
                val insets = AppBarDefaults.bottomAppBarWindowInsets
                val bottomInsetPx = insets.getBottom(LocalDensity.current)
                val bottomInsetDp = with(density) { bottomInsetPx.toDp() }
                val keyBoardHeight = keyboardState.isVisible.judge(
                    keyboardState.height - bottomInsetDp,
                    0.dp
                )

                val xOffset = with(density) {
                    // 屏幕水平居中
                    val screenWidth = screenWidthDp.dp.toPx()
                    ((screenWidth - popupWidth.toPx()) / 2).toInt()
                }

                val yOffset = areaHeight - (popupHeight + space + keyBoardHeight).toPx

                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(xOffset, yOffset.toInt())
                ) {
                    Box(
                        modifier = Modifier
                    ) {
                        BackgroundColorPicker(
                            selectedColor = state.textBgColor,
                            onColorSelected = { color -> item.onColorChange(color, item) }
                        )
                    }
                }
            }
        }
        else -> {
            // 其他类型不处理
        }
    }
    if(isShowColorPicker){
        PhoneRichColorPickerPopup(
            onDismissRequest = {
                isShowColorPicker = false
                item.onToggleExpanded(item)
            },
            onConfirm = { color ->
                item.onColorChange(color,item)
                item.collectTextColor(color)
                isShowColorPicker = false
            },
            areaHeight =areaHeight,
            menuBarItem = menuBarItem,

        )
    }
}


@Composable
internal fun TableRenderColorPickerPopup(
    item: RichTextToolBarItem.ColorPicker,
    state: RichTextToolBarState,
    offsetYPx: Int
) {

    var lastTimeDismiss by remember { mutableLongStateOf(0L) }

    val closePopupComposable: () -> Unit = {
        lastTimeDismiss = System.currentTimeMillis()
         state.setPopupComposable(null)
    }

    fun handleToolClick(
        offTimeThreshold: Long = 200L,
        popupSetter: @Composable (areaHeight:Int) -> Unit
    ) {
        val offTime = System.currentTimeMillis() - lastTimeDismiss
        if (offTime > offTimeThreshold) {
            state.setPopupComposable { areaHeight ->
                popupSetter(areaHeight) // 显示具体的工具弹窗
            }
        } else {
            closePopupComposable()
        }
    }
    when (item.toolType) {
        RichTextToolType.TEXT_COLOR -> {
            if (state.showTextColorPicker) {
                handleToolClick(
                    popupSetter = { areaHeight ->
                        var isShowColorPicker  by remember { mutableStateOf(false) }

                        val popupHeight =129.dp
                        val popupWidth = 326.dp
                        val space = 8.dp
                        val screenWidthDp = GlobalContext.screenWidth.px2dp
                        val density = LocalDensity.current
                        val keyboardState by rememberKeyboardState()
                        val insets = AppBarDefaults.bottomAppBarWindowInsets
                        val bottomInsetPx = insets.getBottom(LocalDensity.current)
                        val bottomInsetDp = with(density) { bottomInsetPx.toDp() }
                        val keyBoardHeight = keyboardState.isVisible.judge(
                            keyboardState.height - bottomInsetDp,
                            0.dp
                        )

                        val xOffset = with(density) {
                            // 屏幕水平居中
                            val screenWidth = screenWidthDp.dp.toPx()
                            ((screenWidth - popupWidth.toPx()) / 2).toInt()
                        }

                        val yOffset = areaHeight - (popupHeight + space + keyBoardHeight).toPx


                        if(!isShowColorPicker){

                            AnimatedPopupWithShadow(
                                onDismissRequest = {
                                    item.onToggleExpanded(item)
                                    if(!isShowColorPicker){

                                        closePopupComposable()
                                    }
                                },
                                offset = IntOffset(xOffset, yOffset.toInt()) // 弹框高度+弹框与bar间距
                            ) {
                                TextColorPicker(
                                    colors = state.richTextRecentColors,
                                    selectedColor = state.textColor,
                                    onColorSelected = { color -> item.onColorChange(color, item) },
                                    onOpenColorPicker = {
                                        isShowColorPicker =true
                                    },
                                    onClose = {
                                        item.onToggleExpanded(item)
                                        closePopupComposable()
                                    }

                                )
                            }

                        }else{
                            TableRichColorPickerPopup(
                                areaHeight =areaHeight,
                                onDismissRequest = {
                                    item.onToggleExpanded(item)
                                    isShowColorPicker =false
                                    closePopupComposable()
                                },

                                onConfirm = { color ->
                                    item.onColorChange(color,item)
                                    item.collectTextColor(color)
                                })
                        }
                    }
                )


            }
        }
        RichTextToolType.TEXT_BG_COLOR -> {
            if (state.showTextBgColorPicker) {
                Popup(
                    alignment = Alignment.TopCenter,
                    offset = IntOffset(0, -offsetYPx)) {
                    Box(
                        modifier = Modifier
                    ) {
                        BackgroundColorPicker(
                            selectedColor = state.textBgColor,
                            onColorSelected = { color -> item.onColorChange(color, item) }
                        )
                    }
                }
            }
        }
        else -> {
            // 其他类型不处理
        }
    }

}

@Composable
fun TableRichColorPickerPopup(
    areaHeight:Int,
    onDismissRequest: () -> Unit,
    onConfirm:(color:Color) ->Unit,
){
    val popupHeight:Dp =  381.dp
    val dimens = getGlobalDimens()
    val popupWidth = dimens.colorPaletteWidth
    val space = 8.dp
    val screenWidthDp = GlobalContext.screenWidth.px2dp
    val density = LocalDensity.current
    val keyboardState by rememberKeyboardState()
    val insets = AppBarDefaults.bottomAppBarWindowInsets
    val bottomInsetPx = insets.getBottom(LocalDensity.current)
    val bottomInsetDp = with(density) { bottomInsetPx.toDp() }
    val keyBoardHeight = keyboardState.isVisible.judge(
        keyboardState.height - bottomInsetDp,
        0.dp
    )

    val xOffset = with(density) {
        // 屏幕水平居中
        val screenWidth = screenWidthDp.dp.toPx()
        ((screenWidth - popupWidth.toPx()) / 2).toInt()
    }

    val yOffset = areaHeight - (popupHeight + space + keyBoardHeight).toPx

    AnimatedPopupWithShadow(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset, yOffset.toInt()),
    ){ closePopup ->

        PureColorPalettePopup(
            modifier = Modifier.defShadow(20.dp),
            popupWidth = popupWidth,
            popupHeight = popupHeight,
            onConfirm = onConfirm,
            onClosePopup = closePopup
        )

    }

}



@Composable
fun PhoneRichColorPickerPopup(
    onDismissRequest: () -> Unit,
    onConfirm:(color:Color) ->Unit,
    areaHeight: Int,
    menuBarItem: MenuBarItem? = null
){
    val space = 8.dp
    val popupHeight:Dp =  381.dp
    val dimens = getGlobalDimens()
    val popupWidth = dimens.colorPaletteWidth
    val density = LocalDensity.current
    val screenWidthPx= GlobalContext.screenWidth

    val keyboardState by rememberKeyboardState()
    val insets = AppBarDefaults.bottomAppBarWindowInsets
    val bottomInsetPx = insets.getBottom(LocalDensity.current)
    val bottomInsetDp = with(density) { bottomInsetPx.toDp() }
    val keyBoardHeight = keyboardState.isVisible.judge(
        keyboardState.height - bottomInsetDp,
        0.dp
    )

    val xOffset = with(density) {

        // 屏幕水平居中：(屏幕宽度 - 弹窗宽度) / 2
        ((screenWidthPx - popupWidth.toPx()) / 2).toInt()
    }

    val yOffset = areaHeight - (popupHeight + space + keyBoardHeight).toPx
    AnimatedPopupWithShadow(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset, yOffset.toInt()),
    ){ closePopup ->

        PureColorPalettePopup(
            popupWidth = popupWidth,
            popupHeight = popupHeight,
            onConfirm = onConfirm,
            onClosePopup = closePopup
        )

    }
}