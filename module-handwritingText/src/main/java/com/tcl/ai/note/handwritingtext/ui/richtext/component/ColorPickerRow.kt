package com.tcl.ai.note.handwritingtext.ui.richtext.component

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.ui.widget.CustomRadioButton
import com.tcl.ai.note.handwritingtext.utils.ColorUtils
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.SemanticsUtils.buttonSemantics
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.drawableRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.IconThemeSwitcher
import com.tcl.ai.note.widget.clickableHover
import com.tcl.ai.note.widget.clickableNoRipple
import kotlinx.coroutines.launch

/**
 * 颜色选择器类型
 */
enum class ColorPickerType {
    TEXT_COLOR,      // 文本颜色选择器（不含透明色）
    BACKGROUND_COLOR // 背景颜色选择器（含透明色）
}

/**
 * 可复用的颜色选择器行组件
 * 
 * @param colors 颜色列表
 * @param selectedColor 当前选中的颜色
 * @param type 选择器类型（文本颜色或背景颜色）
 * @param onColorSelected 颜色选择回调
 * @param modifier 修饰符
 */
@Composable
fun ColorPickerRow(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Black,
    type: ColorPickerType = ColorPickerType.TEXT_COLOR,
    onColorSelected: (Color) -> Unit = {},
    onOpenColorPicker: () -> Unit ={},
    onClose:() -> Unit ={},
    modifier: Modifier = Modifier
) {

    val colorSize = 18.dp

    Column(
        modifier = modifier
            .background(color = TclTheme.colorScheme.tertiaryBackground)
            .clip(RoundedCornerShape(20.dp))
            .padding(horizontal = 12.dp)
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        Row(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ){
            Text(
                modifier = Modifier.fillMaxWidth()
                    .weight(1f),
                fontSize = 20.sp,
                lineHeight = 24.sp,
                textAlign = TextAlign.Start,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Medium,
                color = TclTheme.colorScheme.textDialogTitle,
                text = com.tcl.ai.note.base.R.string.rich_text_font_color.stringRes(),
            )
            HoverProofIconButton(
                modifier = Modifier.size(20.dp),
                onClick = {
                    onClose()
                }
            ) {
                Icon(
                    painter = R.drawable.ic_rich_color_close_btn.drawableRes(),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.close),
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))
        Row(
            modifier = Modifier.width(302.dp)
                .padding(horizontal = 12.dp)
                .height(48.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 仅背景颜色选择器包含透明选项
            if (type == ColorPickerType.BACKGROUND_COLOR) {
                // 透明色按钮
                TransparentColorButton(
                    isSelected = selectedColor == Color.Transparent,
                    size = colorSize,
                    onClick = { onColorSelected(Color.Transparent) }
                )
                
                // 透明按钮与分割线之间的距离
                Spacer(modifier = Modifier.width(10.dp))
                
                // 分割线，高度为16dp，宽度为1dp
                Divider(
                    modifier = Modifier
                        .height(16.dp)
                        .width(1.dp),
                    color = Color.Black.copy(alpha = 0.1f)
                )
                
                // 分割线与黑色按钮之间的距离
                Spacer(modifier = Modifier.width(10.dp))
            }
            
            // 渲染颜色选项
            colors.forEachIndexed { index, color ->
                CustomRadioButton(
                    color =    isDarkTheme.judge(color.inverseColor(),color),
                    selected = color == selectedColor,
                    onClick = { onColorSelected(color) }
                )
            }

            Box(
                modifier = Modifier.size(20.dp)
                    .invisibleSemantics()
                    .clickableNoRipple {
                        onOpenColorPicker()
                    },
                contentAlignment = Alignment.Center,
            ){
                Image(
                    painter = R.drawable.ic_colour_disc.drawableRes(),
                    contentDescription = "",
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * 单个颜色按钮
 */
@Composable
private fun ColorButton(
    color: Color,
    isSelected: Boolean,
    size: androidx.compose.ui.unit.Dp,
    onClick: () -> Unit
) {
    if (isSelected) {
        // 选中状态：外环和内圆都是颜色本身，中间是白色环
        Box(
            modifier = Modifier
                .size(size)
                .shadow(
                    elevation = 2.dp, 
                    shape = CircleShape,
                    ambientColor = Color(0x26000000),
                    spotColor = Color(0x26000000)
                )
                .clip(CircleShape)
                .background(color)
                .clickable(onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            // 白色环
            Box(
                modifier = Modifier
                    .size(size - 4.dp)
                    .clip(CircleShape)
                    .background(Color.White)
            ) {
                // 内部颜色圆
                Box(
                    modifier = Modifier
                        .size(size - 8.dp)
                        .clip(CircleShape)
                        .background(color)
                        .align(Alignment.Center)
                )
            }
        }
    } else {
        // 未选中状态：简单的颜色圆
        Box(
            modifier = Modifier
                .size(size)
                .clip(CircleShape)
                .background(color)
                .clickable(onClick = onClick)
        )
    }
}

/**
 * 透明颜色按钮（用于背景色选择）
 */
@Composable
private fun TransparentColorButton(
    isSelected: Boolean,
    size: androidx.compose.ui.unit.Dp,
    onClick: () -> Unit
) {
    // 透明色按钮无论是否选中，都保持相同的视觉效果
    Box(
        modifier = Modifier
            .size(size)
            .clip(CircleShape)
            .background(Color.White)
            .border(
                width = 0.5.dp,
                color = Color.Gray.copy(alpha = 0.3f),
                shape = CircleShape
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        TransparentColorIcon(
            modifier = Modifier.size(size * 0.8f),
            cellSize = 4f
        )
    }
}

/**
 * 预览用的固定尺寸颜色选择器
 */
@Composable
fun TextColorPicker(
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Black,
    onColorSelected: (Color) -> Unit = {},
    onOpenColorPicker: () -> Unit ={},
    onClose:() -> Unit ={},
) {
    ColorPickerRow(
        colors = colors,
        selectedColor = selectedColor,
        type = ColorPickerType.TEXT_COLOR,
        onColorSelected = onColorSelected,
        onOpenColorPicker = onOpenColorPicker,
        onClose = onClose,
        modifier = Modifier.width(326.dp).wrapContentHeight()


    )
}

/**
 * 预览用的固定尺寸背景颜色选择器
 */
@Composable
fun BackgroundColorPicker(
    colors: List<Color> = ColorUtils.TEXT_COLORS,
    selectedColor: Color = Color.Transparent,
    onColorSelected: (Color) -> Unit = {}
) {
    ColorPickerRow(
        colors = colors,
        selectedColor = selectedColor,
        type = ColorPickerType.BACKGROUND_COLOR,
        onColorSelected = onColorSelected,
        modifier = Modifier.width(257.dp).height(38.dp)
    )
} 