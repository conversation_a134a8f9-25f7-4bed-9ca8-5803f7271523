package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup.RenderColorPickerPopup
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup.TableRenderColorPickerPopup
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextBackgroundColorIcon
import com.tcl.ai.note.handwritingtext.ui.richtext.component.TextColorIcon
import com.tcl.ai.note.widget.DelayedBackgroundIconButton

/**
 * 渲染颜色选择器按钮及弹出框
 *
 * @param item 颜色选择器项
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 * @param isEnabled 是否启用
 */
@Composable
internal fun RenderColorPicker(
    item: RichTextToolBarItem.ColorPicker,
    state: RichTextToolBarState,
    offsetYPx: Int,
    isEnabled: Boolean
) {

    val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
        val position = layoutCoordinates.localToWindow(Offset.Zero)
        item.position = position

    }
    // 使用自定义内容或默认按钮
    if (item.customContent != null) {
        item.customContent.invoke(Modifier, item)
    } else {
        // 根据工具类型显示不同的颜色图标
        when (item.toolType) {
            RichTextToolType.TEXT_COLOR -> {

                DelayedBackgroundIconButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    iconSize = item.btnSize,
                    isChecked = state.isToolActive(item.toolType),
                    contentDescription = stringResource(item.descriptionRes),
                    enabled = isEnabled,
                    onClick = { item.onToggleExpanded(item) },
                    content = {
                        TextColorIcon(
                            currentColor = state.textColor,
                            onColorChanged = { color -> item.onColorChange(color, item) },
                            modifier = Modifier.size(item.btnSize)
                        )
                    }
                )
            }
            RichTextToolType.TEXT_BG_COLOR -> {
                DelayedBackgroundIconButton(
                    modifier = modifierPos,
                    btnSize = item.btnSize,
                    iconSize = item.btnSize,
                    isChecked = state.isToolActive(item.toolType),
                    contentDescription = stringResource(item.descriptionRes),
                    enabled = isEnabled,
                    onClick = { item.onToggleExpanded(item) },
                    content = {
                        TextBackgroundColorIcon(
                            currentColor = state.textBgColor,
                            onColorChanged = { color -> item.onColorChange(color, item) },
                            modifier = Modifier.size(item.btnSize)
                        )
                    }
                )
            }
            else -> {
                // 对于其他类型，仍然使用原来的图标
                item.iconRes?.let { iconRes ->
                    DelayedBackgroundIconButton(
                        btnSize = item.btnSize,
                        iconSize = item.btnSize,
                        painter = painterResource(id = iconRes),
                        isChecked = state.isToolActive(item.toolType),
                        contentDescription = stringResource(item.descriptionRes),
                        enabled = isEnabled,
                        onClick = { item.onToggleExpanded(item) }
                    )
                }
            }
        }

        // 根据工具类型显示相应的颜色选择器弹窗
        TableRenderColorPickerPopup(item, state, offsetYPx)

    }
}