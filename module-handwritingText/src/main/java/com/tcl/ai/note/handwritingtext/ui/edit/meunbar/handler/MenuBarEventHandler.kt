package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.toArgb
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolGroup
import com.tcl.ai.note.handwritingtext.bean.RichTextToolType
import com.tcl.ai.note.handwritingtext.bean.isMarkerPen
import com.tcl.ai.note.handwritingtext.bean.toPenType
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarConfig
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarDependencies
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.TextStylePopup
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.popup.RenderColorPickerPopup
import com.tcl.ai.note.handwritingtext.ui.popup.PhoneEraserToolPopup
import com.tcl.ai.note.handwritingtext.ui.popup.PhonePenColorPalettePopup
import com.tcl.ai.note.handwritingtext.ui.popup.PhoneToolPopup
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.menu.syncToSdk
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.isLiteVersion

/**
 * 菜单栏事件处理器
 * 负责处理所有菜单栏相关的点击事件和业务逻辑
 *
 * 优化后使用依赖组合，减少参数数量
 */
open class MenuBarEventHandler(
    private val dependencies: MenuBarDependencies,
    private val config: MenuBarConfig
) {


    private var lastTimeDismiss by mutableLongStateOf(0L)

    private val closePopupComposable: () -> Unit = {
        config.setPopupComposable(null)
    }

    private fun handleToolClick(
        offTimeThreshold: Long = 200L,
        popupSetter: @Composable (areaHeight: Int) -> Unit
    ) {
        val offTime = System.currentTimeMillis() - lastTimeDismiss
        if (offTime > offTimeThreshold) {
            config.setPopupComposable { areaHeight ->
                popupSetter(areaHeight)
            }
        } else {
            closePopupComposable()
        }
    }


    /**
     * 处理橡皮擦点击事件
     */
    fun handleEraserClick(menuBarItem: MenuBarItem) {
        with(dependencies) {
            menuBarViewModel.onEraserClick()
            textAndDrawViewModel.editMode = EditMode.DRAW
            // 使用扩展函数同步橡皮擦设置到SDK
            eraserViewModel.syncToSdk(suniaDrawViewModel)
            suniaDrawViewModel.switchStrokeBeautify(false)
        }

        if (menuBarItem.isChecked) {
            handleToolClick(
                popupSetter = { areaHeight ->
                    PhoneEraserToolPopup(
                        eraserViewModel = dependencies.eraserViewModel,
                        suniaDrawViewModel = dependencies.suniaDrawViewModel,
                        areaHeight = areaHeight,
                        onDismissRequest = {
                            lastTimeDismiss = System.currentTimeMillis()
                            closePopupComposable()
                        },
                        onSwitchToBrush = {
                            with(dependencies) {
                                //逻辑调整：适配美化笔，清空画布之前选中的若是美化笔，则清空画布后依然选择美化笔
                                val isBeautifyPenSelected =
                                    menuBarViewModel.menuBarState.value.isBeautifyPenSelected
                                suniaDrawViewModel.switchStrokeBeautify(isBeautifyPenSelected)
                                menuBarViewModel.restoreLastBrushMode(isBeautifyPenSelected)
                                textAndDrawViewModel.editMode = EditMode.DRAW
                                suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
                            }
                        }
                    )
                }
            )
        }
    }

    /**
     * 处理笔刷点击事件
     */
    fun handleBrushClick(menuBarItem: MenuBarItem) {
        with(dependencies) {
            menuBarViewModel.switchToDrawingMode()
            textAndDrawViewModel.editMode = EditMode.DRAW
            suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
            suniaDrawViewModel.switchStrokeBeautify(false)
        }

        if (menuBarItem.isChecked) {
            handleToolClick(
                popupSetter = { areaHeight ->
                    var isShowColorPicker by remember { mutableStateOf(false) }
                    if (!isShowColorPicker) {
                        PhoneToolPopup(
                            isDarkTheme = dependencies.suniaDrawViewModel.isDarkTheme,
                            areaHeight = areaHeight,
                            onOpenColorPicker = {
                                isShowColorPicker = true
                            },
                            onDismissRequest = {
                                if (!isShowColorPicker) {
                                    lastTimeDismiss = System.currentTimeMillis()
                                    closePopupComposable()
                                }
                            },
                            onSwitchBrush = { penProp ->
                                dependencies.suniaDrawViewModel.switchBrush(penProp)
                                dependencies.colorGroupViewModel.cancelSelectColor()
                            },
                            onChangeBrushSize = { brushSize ->
                                dependencies.suniaDrawViewModel.changePenSize(brushSize)
                            },
                            onChangePenColor = { penColor ->
                                dependencies.suniaDrawViewModel.changePenColor(
                                    penColor.color.toArgb(),
                                    penColor.alpha
                                )
                                dependencies.colorGroupViewModel.cancelSelectColor()
                            }
                        )
                    }
                    if (isShowColorPicker) {
                        PhonePenColorPalettePopup(
                            isDarkTheme = dependencies.suniaDrawViewModel.isDarkTheme,
                            areaHeight = areaHeight,
                            curPenColor = PenColor(
                                color = dependencies.penToolbarViewModel.selectedPen.color,
                                alpha = dependencies.penToolbarViewModel.selectedPen.alpha
                            ),
                            isSetAlpha = dependencies.penToolbarViewModel.selectedPen.isMarkerPen() && !isLiteVersion,
                            onConfirm = { penColor ->
                                with(dependencies) {
                                    suniaDrawViewModel.changePenColor(
                                        penColor.color.toArgb(),
                                        penColor.alpha
                                    )
                                    penToolbarViewModel.collectColor(penColor)
                                    penToolbarViewModel.updateSelectedPen(
                                        color = penColor.color,
                                        alpha = penColor.alpha,
                                    )
                                    colorGroupViewModel.cancelSelectColor()
                                }
                            },
                            onDismissRequest = {
                                isShowColorPicker = false
                                lastTimeDismiss = System.currentTimeMillis()
                                closePopupComposable()
                            }
                        )
                    }
                }
            )
        }
    }

    /**
     * 处理键盘切换事件（绘图模式到文本模式）
     */
    fun handleKeyboardSwitchFromDraw() {
        with(dependencies) {
            menuBarViewModel.switchToTextEditMode()
            textAndDrawViewModel.editMode = EditMode.TEXT
            suniaDrawViewModel.switchStrokeBeautify(false)
            // 取消框选状态
            suniaDrawViewModel.finishSelect()
        }
    }

    /**
     * 处理画笔切换事件（文本模式到绘图模式）
     */
    fun handleBrushSwitchFromText() {
        with(dependencies) {
            // 切换到绘图模式，保留所有工具状态
            menuBarViewModel.switchToDrawingMode(preserveToolState = true)
            textAndDrawViewModel.editMode = EditMode.DRAW

            // 根据当前状态同步到SDK
            syncDrawingToolToSdk()
        }
    }

    /**
     * 根据当前菜单栏状态同步对应的工具到SDK
     */
    private fun syncDrawingToolToSdk() {
        with(dependencies) {
            val currentState = menuBarViewModel.menuBarState.value
            when {
                currentState.isEraserActive || currentState.isPassiveEraserActive -> {
                    // 恢复橡皮擦模式
                    eraserViewModel.syncToSdk(suniaDrawViewModel)
                }

                currentState.isBeautifyActive -> {
                    // 恢复笔迹美化模式
                    suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
                    suniaDrawViewModel.switchStrokeBeautify(true)
                }

                else -> {
                    // 恢复普通画笔模式
                    suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
                    suniaDrawViewModel.switchStrokeBeautify(false)
                }
            }
        }
    }

    /**
     * 处理待办事件
     */
    fun handleTodoClick() {
        dependencies.richTextToolBarViewModel.handleTodoToggle(
            RichTextToolBarItem.ToggleButton(
                id = "todo_list",
                toolType = RichTextToolType.TODO_LIST,
                group = RichTextToolGroup.PARAGRAPH,
                onToggle = dependencies.richTextToolBarViewModel::handleTodoToggle
            )
        )
    }

    /**
     * 处理文本样式事件
     */
    fun handleTextStyleClick(menuBarItem: MenuBarItem) {
        handleToolClick(
            popupSetter = { areaHeight ->
                val toolBarState by dependencies.richTextToolBarViewModel.toolBarState.collectAsState()
                val richTextState by dependencies.richTextViewModel.uiState.collectAsState()
                val showAudioPanel by dependencies.audioToTextViewModel.topAudioVisibleState.collectAsState()
                val recordingState by dependencies.recordingViewModel.recordState.collectAsState()

                // 判断录音块是否显示：有录音文件且面板可见且没有正在录音
                // 这个逻辑与TabletTopAudioBlock中的isPanelVisible逻辑保持一致
                val isAudioBlockVisible = richTextState.audios.isNotEmpty() &&
                        if (!recordingState.isRecording) showAudioPanel else false

                TextStylePopup(
                    areaHeight = areaHeight,
                    menuBarItem = menuBarItem,
                    onDismissRequest = {
                        lastTimeDismiss = System.currentTimeMillis()
                        closePopupComposable()
                    },
                    onBulletListClick = {
                        dependencies.richTextToolBarViewModel.handleBulletListToggle(
                            RichTextToolBarItem.ToggleButton(
                                id = "bullet_list",
                                toolType = RichTextToolType.BULLET_LIST,
                                group = RichTextToolGroup.PARAGRAPH,
                                onToggle = dependencies.richTextToolBarViewModel::handleBulletListToggle
                            )
                        )
                    },
                    onNumberListClick = {
                        dependencies.richTextToolBarViewModel.handleNumberListToggle(
                            RichTextToolBarItem.ToggleButton(
                                id = "number_list",
                                toolType = RichTextToolType.NUMBER_LIST,
                                group = RichTextToolGroup.PARAGRAPH,
                                onToggle = dependencies.richTextToolBarViewModel::handleNumberListToggle
                            )
                        )
                    },
                    onBoldClick = {
                        dependencies.richTextToolBarViewModel.handleBoldToggle(
                            RichTextToolBarItem.ToggleButton(
                                id = "bold",
                                toolType = RichTextToolType.BOLD,
                                group = RichTextToolGroup.TEXT_FORMAT,
                                onToggle = dependencies.richTextToolBarViewModel::handleBoldToggle
                            )
                        )
                    },
                    onUnderlineClick = {
                        dependencies.richTextToolBarViewModel.handleUnderlineToggle(
                            RichTextToolBarItem.ToggleButton(
                                id = "underline",
                                toolType = RichTextToolType.UNDERLINE,
                                group = RichTextToolGroup.TEXT_FORMAT,
                                onToggle = dependencies.richTextToolBarViewModel::handleUnderlineToggle
                            )
                        )
                    },
                    onItalicClick = {
                        dependencies.richTextToolBarViewModel.handleItalicToggle(
                            RichTextToolBarItem.ToggleButton(
                                id = "italic",
                                toolType = RichTextToolType.ITALIC,
                                group = RichTextToolGroup.TEXT_FORMAT,
                                onToggle = dependencies.richTextToolBarViewModel::handleItalicToggle
                            )
                        )
                    },
                    isBoldActive = toolBarState.isToolActive(RichTextToolType.BOLD),
                    isUnderlineActive = toolBarState.isToolActive(RichTextToolType.UNDERLINE),
                    isItalicActive = toolBarState.isToolActive(RichTextToolType.ITALIC),
                    isBulletListActive = toolBarState.isToolActive(RichTextToolType.BULLET_LIST),
                    isNumberListActive = toolBarState.isToolActive(RichTextToolType.NUMBER_LIST),
                    isAudioBlockVisible = isAudioBlockVisible
                )
            }
        )
    }

    /**
     * 处理绘图撤销事件
     */
    fun handleDrawUndo() {
        dependencies.textAndDrawViewModel.undo()
    }

    /**
     * 处理绘图重做事件
     */
    fun handleDrawRedo() {
        dependencies.textAndDrawViewModel.redo()
    }

    /**
     * 处理文本撤销事件
     */
    fun handleTextUndo() {
        dependencies.richTextToolBarViewModel.triggerOperateActionEvent(RichTextOperateEvent.Undo)
    }

    /**
     * 处理文本重做事件
     */
    fun handleTextRedo() {
        dependencies.richTextToolBarViewModel.triggerOperateActionEvent(RichTextOperateEvent.Redo)
    }

    /**
     * 处理美化事件
     */
    fun handleBeautifyClick(menuBarItem: MenuBarItem) {
        dependencies.suniaDrawViewModel.switchBrush(dependencies.suniaDrawViewModel.penPropState.value)
        dependencies.suniaDrawViewModel.switchStrokeBeautify(true)
        dependencies.menuBarViewModel.onBeautifyClick()
        // 自定义美化参数入口，产品已确定好美化参数，屏蔽调试入口
//        if (menuBarItem.isChecked){
//            handleToolClick(
//                popupSetter = { areaHeight ->
//                    BeautifyParamsPop(
//                        strokeBeautifyViewModel = dependencies.strokeBeautifyViewModel,
//                        areaHeight = areaHeight,
//                        onDismissRequest = {
//                            lastTimeDismiss = System.currentTimeMillis()
//                            closePopupComposable()
//                        },
//                        onConfirm = {
//                            Logger.d("MenuBarEventHandler","handleBeautifyClick: ${it.param1}, ${it.param2}, ${it.param3}")
//                            dependencies.strokeBeautifyViewModel.setBeautifyParam(it.param1,it.param2,it.param3)
//                        }
//                    )
//                }
//            )
//        }
    }

    /**
     * 处理AI工具事件
     */
    fun handleAiClick() {
        // TODO AI工具
        dependencies.richTextToolBarViewModel.operateBottomAIPop(true)

    }

    /**
     * 处理文本颜色点击事件
     */
    fun handleTextColorClick(menuBarItem: MenuBarItem) {
        // 如果按钮已经是选中状态（弹框已显示），则关闭弹框
        if (menuBarItem.isChecked) {
            // 更新状态，关闭颜色选择器
            dependencies.richTextToolBarViewModel.handleTextColorToggle(
                RichTextToolBarItem.ColorPicker(
                    id = "text_color",
                    toolType = RichTextToolType.TEXT_COLOR,
                    group = RichTextToolGroup.COLOR,
                    selectedColor = androidx.compose.ui.graphics.Color.Black,
                    onColorChange = { _, _ -> },
                    onToggleExpanded = { }
                )
            )
            lastTimeDismiss = System.currentTimeMillis()
            closePopupComposable()
            return
        }

        // 否则显示弹框，同时更新状态
        // 先更新状态，显示颜色选择器
        dependencies.richTextToolBarViewModel.handleTextColorToggle(
            RichTextToolBarItem.ColorPicker(
                id = "text_color",
                toolType = RichTextToolType.TEXT_COLOR,
                group = RichTextToolGroup.COLOR,
                selectedColor = androidx.compose.ui.graphics.Color.Black,
                onColorChange = { _, _ -> },
                onToggleExpanded = { }
            )
        )

        handleToolClick(
            popupSetter = { areaHeight ->
                val toolBarState by dependencies.richTextToolBarViewModel.toolBarState.collectAsState()

                // 创建颜色选择器项并设置showTextColorPicker为true
                val colorPickerItem = RichTextToolBarItem.ColorPicker(
                    id = "text_color",
                    toolType = RichTextToolType.TEXT_COLOR,
                    group = RichTextToolGroup.COLOR,
                    selectedColor = toolBarState.textColor,
                    onColorChange = { color, item ->
                        dependencies.richTextToolBarViewModel.handleTextColorChange(color, item)
                        // 选择颜色后关闭弹框，同时更新状态
                        dependencies.richTextToolBarViewModel.handleTextColorToggle(item)
                        lastTimeDismiss = System.currentTimeMillis()
                        closePopupComposable()
                    },
                    onToggleExpanded = { item ->
                        // 关闭弹框，同时更新状态
                        dependencies.richTextToolBarViewModel.handleTextColorToggle(item)
                        lastTimeDismiss = System.currentTimeMillis()
                        closePopupComposable()
                    },
                    collectTextColor ={ color ->
                        dependencies.richTextToolBarViewModel.collectTextColor(color)
                    }
                )

                // 创建一个修改后的状态，将showTextColorPicker设为true
                val modifiedState = toolBarState.copy(showTextColorPicker = true)

                // 渲染颜色选择器弹框
                RenderColorPickerPopup(
                    item = colorPickerItem,
                    state = modifiedState,
                    areaHeight = areaHeight,
                    menuBarItem = menuBarItem
                )
            }
        )
    }

    /**
     * 处理文本背景颜色点击事件
     */
    fun handleTextBgColorClick(menuBarItem: MenuBarItem) {
        ToastUtils.makeWithCancel("文本背景颜色")
    }

    /**
     * 处理AI工具事件
     */
    fun handleLassoClick() {
        with(dependencies) {
            menuBarViewModel.onLassoClick()
            textAndDrawViewModel.editMode = EditMode.DRAW
            dependencies.suniaDrawViewModel.setLassoEnable(true)
        }
    }

    /**
     * 初始化笔刷设置
     */
    fun initializeBrush() {
        with(dependencies) {
            if (penToolbarViewModel.isInit) {
                val selectedPen = penToolbarViewModel.selectedPen
                suniaDrawViewModel.switchBrush(PenProp().apply {
                    penType = selectedPen.toPenType().value
                    penColor = selectedPen.color.toArgb()
                    penSize = selectedPen.width
                    penAlpha = selectedPen.alpha
                })
            }
        }
    }

    companion object {
        /**
         * 定义所有需要清理的 MenuBarItem data object
         * 避免内存泄露：data object 是全局单例，会持有 MenuBarEventHandler 的引用
         * 注意：新增 MenuBarItem 时，需要在此方法中添加对应的清理代码
         * 不使用反射的话，就得以列表的方式进行维护
         */
        fun cleanup() {
            val menuItems = listOf(
                MenuBarItem.Keyboard,
                MenuBarItem.Brush,
                MenuBarItem.Eraser,
                MenuBarItem.Lasso,
                MenuBarItem.Ruler,
                MenuBarItem.HandwritingToText,
                MenuBarItem.Undo,
                MenuBarItem.Redo,
                MenuBarItem.Beautify,
                MenuBarItem.PanOnly,
                MenuBarItem.ShapeRecognize,
                MenuBarItem.TODO,
                MenuBarItem.TextStyle
            )

            // 批量清理 onClick 引用
            menuItems.forEach { item ->
                item.onClick = {}
            }
        }
    }

    fun cleanupPopup() {
        // 同时清理内部持有的状态
        closePopupComposable()
    }

}
